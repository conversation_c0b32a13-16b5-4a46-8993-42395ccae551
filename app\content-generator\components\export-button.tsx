"use client";

import React, { useState } from 'react';
import { GeneratedFile } from '../types';

interface ExportButtonProps {
  files: GeneratedFile[];
  disabled?: boolean;
}

interface ExportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onExport: (type: 'pdf' | 'ppt') => void;
  slideFiles: GeneratedFile[];
}

// 检测是否为slide内容
const isSlideContent = (content: string): boolean => {
  return content.includes('<div class="slide"') || content.includes('class="slide');
};

// 导出选择模态框
const ExportModal: React.FC<ExportModalProps> = ({ isOpen, onClose, onExport, slideFiles }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">导出幻灯片</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="mb-4">
          <p className="text-gray-600 mb-3">
            检测到 {slideFiles.length} 个幻灯片文件，请选择导出格式：
          </p>
          <div className="space-y-2 text-sm text-gray-500">
            {slideFiles.map((file, index) => (
              <div key={file.id} className="flex items-center">
                <svg className="w-4 h-4 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <span>{file.name}</span>
              </div>
            ))}
          </div>
        </div>

        <div className="flex flex-col gap-3">
          <button
            onClick={() => onExport('pdf')}
            className="flex items-center justify-center px-4 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            导出为 PDF
          </button>

          <button
            onClick={() => onExport('ppt')}
            className="flex items-center justify-center px-4 py-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            导出为 PPT 格式
          </button>
        </div>

        <div className="mt-4 text-xs text-gray-500">
          <p>• PDF：将保持原有样式和排版</p>
          <p>• PPT：生成PowerPoint兼容的HTML文件</p>
        </div>
      </div>
    </div>
  );
};

const ExportButton: React.FC<ExportButtonProps> = ({ files, disabled = false }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  // 筛选出slide类型的文件
  const slideFiles = files.filter(file =>
    file.status === 'completed' &&
    file.contentType === 'html' &&
    isSlideContent(file.content)
  );

  // 如果没有slide文件，不显示导出按钮
  if (slideFiles.length === 0) {
    return null;
  }

  const handleExport = async (type: 'pdf' | 'ppt') => {
    setIsExporting(true);
    setIsModalOpen(false);

    try {
      if (type === 'pdf') {
        await exportToPDF(slideFiles);
      } else {
        await exportToPPT(slideFiles);
      }
    } catch (error) {
      console.error('导出失败:', error);
      alert('导出失败，请查看控制台获取详细信息');
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <>
      <button
        onClick={() => setIsModalOpen(true)}
        disabled={disabled || isExporting}
        className="px-3 py-1.5 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-lg hover:shadow-md transition-all duration-200 text-sm flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isExporting ? (
          <>
            <svg className="animate-spin h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            导出中...
          </>
        ) : (
          <>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            导出
          </>
        )}
      </button>

      <ExportModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onExport={handleExport}
        slideFiles={slideFiles}
      />
    </>
  );
};

// PDF导出功能
const exportToPDF = async (slideFiles: GeneratedFile[]) => {
  try {
    // 动态导入html2pdf
    const html2pdf = (await import('html2pdf.js')).default;

    if (slideFiles.length === 1) {
      // 单个文件直接导出
      const file = slideFiles[0];
      const element = createTempElement(file.content);

      const opt = {
        margin: 0,
        filename: file.name.replace('.html', '.pdf'),
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: {
          scale: 1.5,
          useCORS: true,
          allowTaint: true,
          width: 1280,
          height: 720,
          scrollX: 0,
          scrollY: 0
        },
        jsPDF: {
          unit: 'px',
          format: [1280, 720],
          orientation: 'landscape'
        }
      };

      await html2pdf().set(opt).from(element).save();
      cleanupTempElement(element);
    } else {
      // 多个文件分别导出然后合并
      for (let i = 0; i < slideFiles.length; i++) {
        const file = slideFiles[i];
        const element = createTempElement(file.content);

        const opt = {
          margin: 0,
          filename: `${file.name.replace('.html', '')}-${i + 1}.pdf`,
          image: { type: 'jpeg', quality: 0.98 },
          html2canvas: {
            scale: 1.5,
            useCORS: true,
            allowTaint: true,
            width: 1280,
            height: 720,
            scrollX: 0,
            scrollY: 0
          },
          jsPDF: {
            unit: 'px',
            format: [1280, 720],
            orientation: 'landscape'
          }
        };

        await html2pdf().set(opt).from(element).save();
        cleanupTempElement(element);

        // 添加延迟避免浏览器阻塞
        if (i < slideFiles.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
    }
  } catch (error) {
    console.error('PDF导出错误:', error);
    throw new Error('PDF导出失败，请确保浏览器支持该功能');
  }
};

// PPT导出功能（生成PowerPoint兼容的HTML）
const exportToPPT = async (slideFiles: GeneratedFile[]) => {
  // 创建PowerPoint兼容的HTML结构
  const pptHtml = generatePPTHTML(slideFiles);

  const blob = new Blob([pptHtml], { type: 'text/html' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `slides-presentation-${new Date().toISOString().slice(0, 10)}.html`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

// 创建临时DOM元素用于PDF生成
const createTempElement = (content: string): HTMLElement => {
  const container = document.createElement('div');
  container.style.position = 'absolute';
  container.style.left = '-9999px';
  container.style.top = '-9999px';
  container.style.width = '1280px';
  container.style.height = '720px';
  container.style.overflow = 'hidden';
  container.style.background = 'white';

  // 如果内容包含完整的HTML文档，提取body内容
  if (content.includes('<!DOCTYPE') || content.includes('<html')) {
    const parser = new DOMParser();
    const doc = parser.parseFromString(content, 'text/html');
    const bodyContent = doc.body?.innerHTML || content;
    container.innerHTML = bodyContent;

    // 复制head中的样式
    const styles = doc.head?.querySelectorAll('style');
    styles?.forEach(style => {
      const newStyle = document.createElement('style');
      newStyle.textContent = style.textContent;
      newStyle.setAttribute('data-temp', 'true');
      document.head.appendChild(newStyle);
      // 记录添加的样式，以便后续清理
      container.setAttribute('data-added-styles', 'true');
    });
  } else {
    container.innerHTML = content;
  }

  document.body.appendChild(container);
  return container;
};

// 清理临时DOM元素和添加的样式
const cleanupTempElement = (element: HTMLElement) => {
  // 清理添加的样式
  if (element.getAttribute('data-added-styles')) {
    const addedStyles = document.head.querySelectorAll('style[data-temp="true"]');
    addedStyles.forEach(style => style.remove());
  }

  // 移除临时元素
  if (element.parentNode) {
    element.parentNode.removeChild(element);
  }
};

// 生成PowerPoint兼容的HTML
const generatePPTHTML = (slideFiles: GeneratedFile[]): string => {
  const slideContents = slideFiles.map((file, index) => {
    // 提取slide内容
    const slideMatch = file.content.match(/<div class="slide"[^>]*>([\s\S]*?)<\/div>/);
    const slideContent = slideMatch ? slideMatch[1] : file.content;

    return `
    <div class="slide" data-slide="${index + 1}">
      ${slideContent}
    </div>`;
  }).join('\n');

  return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>演示文稿 - ${slideFiles.length} 页</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f0f0f0;
        }

        .presentation-container {
            max-width: 1280px;
            margin: 0 auto;
            padding: 20px;
        }

        .slide {
            width: 1280px;
            min-height: 720px;
            margin: 20px auto;
            background: white;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            page-break-after: always;
            position: relative;
        }

        .navigation {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
        }

        .slide-counter {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
            z-index: 1000;
        }

        @media print {
            .navigation, .slide-counter { display: none; }
            .slide { margin: 0; box-shadow: none; page-break-after: always; }
        }
    </style>
</head>
<body>
    <div class="navigation">
        <button onclick="window.print()">打印/保存为PDF</button>
    </div>

    <div class="slide-counter">
        共 ${slideFiles.length} 页幻灯片
    </div>

    <div class="presentation-container">
        ${slideContents}
    </div>

    <script>
        // 键盘导航支持
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowDown' || e.key === 'PageDown') {
                window.scrollBy(0, window.innerHeight);
            } else if (e.key === 'ArrowUp' || e.key === 'PageUp') {
                window.scrollBy(0, -window.innerHeight);
            }
        });
    </script>
</body>
</html>`;
};

export default ExportButton;
