"use client";

import React, { useState } from 'react';
import { GeneratedFile } from '../types';

interface ExportButtonProps {
  files: GeneratedFile[];
  disabled?: boolean;
}

interface ExportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onExport: (type: 'pdf' | 'ppt') => void;
  slideFiles: GeneratedFile[];
}

// 检测是否为slide内容
const isSlideContent = (content: string): boolean => {
  return content.includes('<div class="slide"') || content.includes('class="slide');
};

// 导出选择模态框
const ExportModal: React.FC<ExportModalProps> = ({ isOpen, onClose, onExport, slideFiles }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">导出幻灯片</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="mb-4">
          <p className="text-gray-600 mb-3">
            检测到 {slideFiles.length} 个幻灯片文件，请选择导出格式：
          </p>
          <div className="space-y-2 text-sm text-gray-500">
            {slideFiles.map((file, index) => (
              <div key={file.id} className="flex items-center">
                <svg className="w-4 h-4 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <span>{file.name}</span>
              </div>
            ))}
          </div>
        </div>

        <div className="flex flex-col gap-3">
          <button
            onClick={() => onExport('pdf')}
            className="flex items-center justify-center px-4 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            导出为 PDF
          </button>

          <button
            onClick={() => onExport('ppt')}
            className="flex items-center justify-center px-4 py-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            导出为 PPT 格式
          </button>
        </div>

        <div className="mt-4 text-xs text-gray-500">
          <p>• PDF：将保持原有样式和排版</p>
          <p>• PPT：生成PowerPoint兼容的HTML文件</p>
        </div>
      </div>
    </div>
  );
};

const ExportButton: React.FC<ExportButtonProps> = ({ files, disabled = false }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  // 筛选出slide类型的文件
  const slideFiles = files.filter(file =>
    file.status === 'completed' &&
    file.contentType === 'html' &&
    isSlideContent(file.content)
  );

  // 如果没有slide文件，不显示导出按钮
  if (slideFiles.length === 0) {
    return null;
  }

  const handleExport = async (type: 'pdf' | 'ppt') => {
    setIsExporting(true);
    setIsModalOpen(false);

    try {
      if (type === 'pdf') {
        await exportToPDF(slideFiles);
      } else {
        await exportToPPT(slideFiles);
      }
    } catch (error) {
      console.error('导出失败:', error);
      alert('导出失败，请查看控制台获取详细信息');
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <>
      <button
        onClick={() => setIsModalOpen(true)}
        disabled={disabled || isExporting}
        className="px-3 py-1.5 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-lg hover:shadow-md transition-all duration-200 text-sm flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isExporting ? (
          <>
            <svg className="animate-spin h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            导出中...
          </>
        ) : (
          <>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            导出
          </>
        )}
      </button>

      <ExportModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onExport={handleExport}
        slideFiles={slideFiles}
      />
    </>
  );
};

// PDF导出功能
const exportToPDF = async (slideFiles: GeneratedFile[]) => {
  try {
    // 动态导入html2pdf
    const html2pdf = (await import('html2pdf.js')).default;

    if (slideFiles.length === 1) {
      // 单个文件直接导出
      const file = slideFiles[0];

      // 创建临时元素并等待样式加载
      const element = await createTempElementAsync(file.content);

      const opt = {
        margin: 0,
        filename: file.name.replace('.html', '.pdf'),
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: {
          scale: 2,
          useCORS: true,
          allowTaint: true,
          foreignObjectRendering: true,
          width: 1280,
          height: 720,
          scrollX: 0,
          scrollY: 0,
          backgroundColor: null, // 让背景透明，使用元素自己的背景
          logging: false,
          removeContainer: true,
          async: true
        },
        jsPDF: {
          unit: 'px',
          format: [1280, 720],
          orientation: 'landscape'
        }
      };

      await html2pdf().set(opt).from(element).save();
      cleanupTempElement(element);
    } else {
      // 多个文件合并为一个PDF
      const combinedElement = await createCombinedElementAsync(slideFiles);

      const opt = {
        margin: 0,
        filename: `slides-presentation-${new Date().toISOString().slice(0, 10)}.pdf`,
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: {
          scale: 2,
          useCORS: true,
          allowTaint: true,
          foreignObjectRendering: true,
          width: 1280,
          scrollX: 0,
          scrollY: 0,
          backgroundColor: null,
          logging: false,
          removeContainer: true,
          async: true
        },
        jsPDF: {
          unit: 'px',
          format: [1280, 720],
          orientation: 'landscape'
        }
      };

      // 直接从合并的元素生成PDF，html2pdf会自动分页
      await html2pdf().set(opt).from(combinedElement).save();
      cleanupTempElement(combinedElement);
    }
  } catch (error) {
    console.error('PDF导出错误:', error);
    throw new Error('PDF导出失败，请确保浏览器支持该功能');
  }
};

// PPT导出功能（生成PowerPoint兼容的HTML）
const exportToPPT = async (slideFiles: GeneratedFile[]) => {
  // 创建PowerPoint兼容的HTML结构
  const pptHtml = generatePPTHTML(slideFiles);

  const blob = new Blob([pptHtml], { type: 'text/html' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `slides-presentation-${new Date().toISOString().slice(0, 10)}.html`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

// 获取外部CSS内容
const fetchExternalCSS = async (url: string): Promise<string> => {
  try {
    const response = await fetch(url);
    if (response.ok) {
      return await response.text();
    }
  } catch (error) {
    console.warn('无法获取外部CSS:', url, error);
  }
  return '';
};

// 创建临时DOM元素用于PDF生成（完全内联样式版本）
const createTempElementAsync = async (content: string): Promise<HTMLElement> => {
  const container = document.createElement('div');
  container.style.position = 'absolute';
  container.style.left = '-9999px';
  container.style.top = '-9999px';
  container.style.width = '1280px';
  container.style.height = '720px';
  container.style.overflow = 'visible';
  container.style.background = 'white';
  container.style.fontFamily = 'Microsoft YaHei, Arial, sans-serif';

  if (content.includes('<!DOCTYPE') || content.includes('<html')) {
    const parser = new DOMParser();
    const doc = parser.parseFromString(content, 'text/html');

    // 收集所有内联样式
    let allStyles = '';
    const styles = doc.head?.querySelectorAll('style');
    styles?.forEach(style => {
      if (style.textContent) {
        allStyles += style.textContent + '\n';
      }
    });

    // 获取外部CSS内容
    const links = doc.head?.querySelectorAll('link[rel="stylesheet"]');
    const cssPromises: Promise<string>[] = [];

    links?.forEach(link => {
      const href = (link as HTMLLinkElement).href;
      if (href) {
        cssPromises.push(fetchExternalCSS(href));
      }
    });

    // 等待所有外部CSS加载完成
    const externalCSS = await Promise.all(cssPromises);
    allStyles += externalCSS.join('\n');

    // 添加Tailwind和FontAwesome的关键样式（作为备用）
    allStyles += `
      /* Tailwind基础样式 */
      .text-6xl { font-size: 3.75rem; line-height: 1; }
      .text-5xl { font-size: 3rem; line-height: 1; }
      .text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
      .text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
      .text-2xl { font-size: 1.5rem; line-height: 2rem; }
      .text-xl { font-size: 1.25rem; line-height: 1.75rem; }
      .text-lg { font-size: 1.125rem; line-height: 1.75rem; }
      .text-sm { font-size: 0.875rem; line-height: 1.25rem; }
      .text-xs { font-size: 0.75rem; line-height: 1rem; }
      .font-bold { font-weight: 700; }
      .font-semibold { font-weight: 600; }
      .mb-1 { margin-bottom: 0.25rem; }
      .mb-2 { margin-bottom: 0.5rem; }
      .mb-3 { margin-bottom: 0.75rem; }
      .mb-4 { margin-bottom: 1rem; }
      .mb-6 { margin-bottom: 1.5rem; }
      .mb-8 { margin-bottom: 2rem; }
      .mb-12 { margin-bottom: 3rem; }
      .mt-1 { margin-top: 0.25rem; }
      .mt-2 { margin-top: 0.5rem; }
      .mt-12 { margin-top: 3rem; }
      .mt-16 { margin-top: 4rem; }
      .mr-1 { margin-right: 0.25rem; }
      .mr-2 { margin-right: 0.5rem; }
      .mx-auto { margin-left: auto; margin-right: auto; }
      .p-16 { padding: 4rem; }
      .text-center { text-align: center; }
      .text-white { color: rgb(255 255 255); }
      .text-gray-300 { color: rgb(209 213 219); }
      .text-gray-400 { color: rgb(156 163 175); }
      .text-gray-500 { color: rgb(107 114 128); }
      .text-blue-400 { color: rgb(96 165 250); }
      .text-purple-400 { color: rgb(196 181 253); }
      .text-pink-400 { color: rgb(244 114 182); }
      .grid { display: grid; }
      .grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
      .grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
      .gap-6 { gap: 1.5rem; }
      .gap-8 { gap: 2rem; }
      .flex { display: flex; }
      .flex-col { flex-direction: column; }
      .items-start { align-items: flex-start; }
      .items-center { align-items: center; }
      .justify-center { justify-content: center; }
      .space-y-2 > :not([hidden]) ~ :not([hidden]) { margin-top: 0.5rem; }
      .w-48 { width: 12rem; }
      .w-24 { width: 6rem; }
      .w-16 { width: 4rem; }
      .h-1 { height: 0.25rem; }
      .h-16 { height: 4rem; }
      .max-w-2xl { max-width: 42rem; }
      .max-w-4xl { max-width: 56rem; }
      .rounded-full { border-radius: 9999px; }
      .absolute { position: absolute; }
      .relative { position: relative; }
      .bottom-4 { bottom: 1rem; }
      .right-6 { right: 1.5rem; }
      .z-10 { z-index: 10; }
      .bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)); }
      .from-blue-400 { --tw-gradient-from: #60a5fa; --tw-gradient-to: rgba(96, 165, 250, 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
      .to-purple-500 { --tw-gradient-to: #a855f7; }
      .from-purple-500 { --tw-gradient-from: #a855f7; --tw-gradient-to: rgba(168, 85, 247, 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
      .to-pink-500 { --tw-gradient-to: #ec4899; }

      /* FontAwesome基础样式 */
      .fas { font-family: "Font Awesome 6 Free", "Font Awesome 5 Free", "FontAwesome"; font-weight: 900; }
      .fa-file-pdf:before { content: "\\f1c1"; }
      .fa-presentation:before { content: "\\f1c4"; }
      .fa-magic:before { content: "\\f0d0"; }
      .fa-check:before { content: "\\f00c"; }
      .fa-envelope:before { content: "\\f0e0"; }
      .fa-phone-alt:before { content: "\\f879"; }
      .fa-globe:before { content: "\\f0ac"; }
    `;

    // 创建样式元素
    const styleElement = document.createElement('style');
    styleElement.textContent = allStyles;
    container.appendChild(styleElement);

    // 提取并克隆完整的body内容
    if (doc.body) {
      const clonedBody = doc.body.cloneNode(true) as HTMLElement;
      // 确保body样式正确
      clonedBody.style.margin = '0';
      clonedBody.style.padding = '0';
      clonedBody.style.width = '1280px';
      clonedBody.style.height = '720px';
      clonedBody.style.overflow = 'hidden';

      // 将body的内容添加到容器中
      while (clonedBody.firstChild) {
        container.appendChild(clonedBody.firstChild);
      }
    } else {
      container.innerHTML = content;
    }

    container.setAttribute('data-added-styles', 'true');
  } else {
    container.innerHTML = content;
  }

  document.body.appendChild(container);

  // 等待DOM渲染和样式应用
  return new Promise<HTMLElement>((resolve) => {
    setTimeout(() => resolve(container), 1000); // 增加等待时间
  });
};

// 创建合并的DOM元素用于多页PDF生成（异步版本）
const createCombinedElementAsync = async (slideFiles: GeneratedFile[]): Promise<HTMLElement> => {
  const container = document.createElement('div');
  container.style.position = 'absolute';
  container.style.left = '-9999px';
  container.style.top = '-9999px';
  container.style.width = '1280px';
  container.style.background = 'white';
  container.style.fontFamily = 'Microsoft YaHei, Arial, sans-serif';

  let allStyles = new Set<string>();
  let combinedContent = '';

  // 收集所有外部CSS
  const cssPromises: Promise<string>[] = [];

  for (const file of slideFiles) {
    if (file.content.includes('<!DOCTYPE') || file.content.includes('<html')) {
      const parser = new DOMParser();
      const doc = parser.parseFromString(file.content, 'text/html');

      // 收集内联样式
      const styles = doc.head?.querySelectorAll('style');
      styles?.forEach(style => {
        if (style.textContent) {
          allStyles.add(style.textContent);
        }
      });

      // 收集外部CSS
      const links = doc.head?.querySelectorAll('link[rel="stylesheet"]');
      links?.forEach(link => {
        const href = (link as HTMLLinkElement).href;
        if (href) {
          cssPromises.push(fetchExternalCSS(href));
        }
      });
    }
  }

  // 等待所有外部CSS加载完成
  const externalCSS = await Promise.all(cssPromises);
  externalCSS.forEach(css => {
    if (css) allStyles.add(css);
  });

  // 添加备用样式
  allStyles.add(`
    /* Tailwind基础样式 */
    .text-6xl { font-size: 3.75rem; line-height: 1; }
    .text-5xl { font-size: 3rem; line-height: 1; }
    .text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
    .text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
    .text-2xl { font-size: 1.5rem; line-height: 2rem; }
    .text-xl { font-size: 1.25rem; line-height: 1.75rem; }
    .text-lg { font-size: 1.125rem; line-height: 1.75rem; }
    .text-sm { font-size: 0.875rem; line-height: 1.25rem; }
    .text-xs { font-size: 0.75rem; line-height: 1rem; }
    .font-bold { font-weight: 700; }
    .font-semibold { font-weight: 600; }
    .mb-1 { margin-bottom: 0.25rem; }
    .mb-2 { margin-bottom: 0.5rem; }
    .mb-3 { margin-bottom: 0.75rem; }
    .mb-4 { margin-bottom: 1rem; }
    .mb-6 { margin-bottom: 1.5rem; }
    .mb-8 { margin-bottom: 2rem; }
    .mb-12 { margin-bottom: 3rem; }
    .mt-1 { margin-top: 0.25rem; }
    .mt-2 { margin-top: 0.5rem; }
    .mt-12 { margin-top: 3rem; }
    .mt-16 { margin-top: 4rem; }
    .mr-1 { margin-right: 0.25rem; }
    .mr-2 { margin-right: 0.5rem; }
    .mx-auto { margin-left: auto; margin-right: auto; }
    .p-16 { padding: 4rem; }
    .text-center { text-align: center; }
    .text-white { color: rgb(255 255 255); }
    .text-gray-300 { color: rgb(209 213 219); }
    .text-gray-400 { color: rgb(156 163 175); }
    .text-gray-500 { color: rgb(107 114 128); }
    .text-blue-400 { color: rgb(96 165 250); }
    .text-purple-400 { color: rgb(196 181 253); }
    .text-pink-400 { color: rgb(244 114 182); }
    .grid { display: grid; }
    .grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
    .grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
    .gap-6 { gap: 1.5rem; }
    .gap-8 { gap: 2rem; }
    .flex { display: flex; }
    .flex-col { flex-direction: column; }
    .items-start { align-items: flex-start; }
    .items-center { align-items: center; }
    .justify-center { justify-content: center; }
    .space-y-2 > :not([hidden]) ~ :not([hidden]) { margin-top: 0.5rem; }
    .w-48 { width: 12rem; }
    .w-24 { width: 6rem; }
    .w-16 { width: 4rem; }
    .h-1 { height: 0.25rem; }
    .h-16 { height: 4rem; }
    .max-w-2xl { max-width: 42rem; }
    .max-w-4xl { max-width: 56rem; }
    .rounded-full { border-radius: 9999px; }
    .absolute { position: absolute; }
    .relative { position: relative; }
    .bottom-4 { bottom: 1rem; }
    .right-6 { right: 1.5rem; }
    .z-10 { z-index: 10; }
    .bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)); }
    .from-blue-400 { --tw-gradient-from: #60a5fa; --tw-gradient-to: rgba(96, 165, 250, 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
    .to-purple-500 { --tw-gradient-to: #a855f7; }
    .from-purple-500 { --tw-gradient-from: #a855f7; --tw-gradient-to: rgba(168, 85, 247, 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
    .to-pink-500 { --tw-gradient-to: #ec4899; }

    /* FontAwesome基础样式 */
    .fas { font-family: "Font Awesome 6 Free", "Font Awesome 5 Free", "FontAwesome"; font-weight: 900; }
    .fa-file-pdf:before { content: "\\f1c1"; }
    .fa-presentation:before { content: "\\f1c4"; }
    .fa-magic:before { content: "\\f0d0"; }
    .fa-check:before { content: "\\f00c"; }
    .fa-envelope:before { content: "\\f0e0"; }
    .fa-phone-alt:before { content: "\\f879"; }
    .fa-globe:before { content: "\\f0ac"; }
  `);

  // 创建样式元素
  const styleElement = document.createElement('style');
  styleElement.textContent = Array.from(allStyles).join('\n');
  container.appendChild(styleElement);

  // 处理每个文件的内容
  slideFiles.forEach((file, index) => {
    if (file.content.includes('<!DOCTYPE') || file.content.includes('<html')) {
      const parser = new DOMParser();
      const doc = parser.parseFromString(file.content, 'text/html');

      // 提取完整的body内容
      const bodyContent = doc.body?.innerHTML || file.content;
      combinedContent += `
        <div class="slide-page" style="
          width: 1280px;
          height: 720px;
          page-break-after: always;
          position: relative;
          margin: 0;
          padding: 0;
          overflow: hidden;
          ${index > 0 ? 'page-break-before: always;' : ''}
        ">
          ${bodyContent}
        </div>
      `;
    } else {
      combinedContent += `
        <div class="slide-page" style="
          width: 1280px;
          height: 720px;
          page-break-after: always;
          position: relative;
          margin: 0;
          padding: 0;
          overflow: hidden;
          ${index > 0 ? 'page-break-before: always;' : ''}
        ">
          ${file.content}
        </div>
      `;
    }
  });

  container.innerHTML += combinedContent;
  container.setAttribute('data-added-styles', 'true');
  document.body.appendChild(container);

  // 等待DOM渲染完成
  return new Promise<HTMLElement>((resolve) => {
    setTimeout(() => resolve(container), 1000);
  });
};

// 清理临时DOM元素和添加的样式
const cleanupTempElement = (element: HTMLElement) => {
  // 清理添加的样式
  if (element.getAttribute('data-added-styles')) {
    const addedStyles = document.head.querySelectorAll('style[data-temp="true"]');
    addedStyles.forEach(style => style.remove());
  }

  // 移除临时元素
  if (element.parentNode) {
    element.parentNode.removeChild(element);
  }
};

// 生成PowerPoint兼容的HTML
const generatePPTHTML = (slideFiles: GeneratedFile[]): string => {
  // 收集所有样式和外部链接
  let allStyles = new Set<string>();
  let allLinks = new Set<string>();

  const slideContents = slideFiles.map((file, index) => {
    let slideContent = '';

    // 如果是完整的HTML文档，提取内容和样式
    if (file.content.includes('<!DOCTYPE') || file.content.includes('<html')) {
      const parser = new DOMParser();
      const doc = parser.parseFromString(file.content, 'text/html');

      // 收集样式
      const styles = doc.head?.querySelectorAll('style');
      styles?.forEach(style => {
        if (style.textContent) {
          allStyles.add(style.textContent);
        }
      });

      // 收集外部链接
      const links = doc.head?.querySelectorAll('link[rel="stylesheet"]');
      links?.forEach(link => {
        const href = (link as HTMLLinkElement).href;
        if (href) {
          allLinks.add(href);
        }
      });

      // 直接使用完整的body内容，不要只提取slide
      slideContent = doc.body?.innerHTML || file.content;
    } else {
      slideContent = file.content;
    }

    return `
    <div class="slide-page" data-slide="${index + 1}" id="slide-${index + 1}">
      ${slideContent}
    </div>`;
  }).join('\n');

  // 合并所有样式
  const combinedStyles = Array.from(allStyles).join('\n');

  // 生成外部链接标签
  const linkTags = Array.from(allLinks).map(href =>
    `<link href="${href}" rel="stylesheet">`
  ).join('\n    ');

  return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>演示文稿 - ${slideFiles.length} 页</title>
    ${linkTags}
    <style>
        body, html {
            margin: 0;
            padding: 0;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f0f0f0;
            scroll-behavior: smooth;
        }

        .presentation-container {
            max-width: 1280px;
            margin: 0 auto;
            padding: 20px;
        }

        .slide-page {
            width: 1280px;
            min-height: 720px;
            margin: 20px auto;
            background: white;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            page-break-after: always;
            position: relative;
            border-radius: 8px;
            overflow: hidden;
        }

        .slide-page .slide {
            width: 1280px;
            height: 720px;
            margin: 0;
            border-radius: 0;
            position: relative;
        }

        .navigation {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 15px;
            border-radius: 8px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .nav-button {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s;
        }

        .nav-button:hover {
            background: rgba(255,255,255,0.3);
        }

        .slide-counter {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            font-size: 14px;
            z-index: 1000;
        }

        .slide-navigation {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            z-index: 1000;
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .slide-nav-button {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s;
        }

        .slide-nav-button:hover {
            background: rgba(255,255,255,0.3);
        }

        .slide-nav-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        @media print {
            .navigation, .slide-counter, .slide-navigation { display: none; }
            .slide-page { margin: 0; box-shadow: none; page-break-after: always; }
        }

        /* 原始幻灯片样式 */
        ${combinedStyles}
    </style>
</head>
<body>
    <div class="navigation">
        <button class="nav-button" onclick="window.print()">打印/保存为PDF</button>
        <button class="nav-button" onclick="scrollToTop()">回到顶部</button>
        <button class="nav-button" onclick="toggleFullscreen()">全屏模式</button>
    </div>

    <div class="slide-counter">
        <span id="current-slide">1</span> / ${slideFiles.length}
    </div>

    <div class="slide-navigation">
        <button class="slide-nav-button" onclick="previousSlide()" id="prev-btn">上一页</button>
        <span id="slide-indicator">第 1 页</span>
        <button class="slide-nav-button" onclick="nextSlide()" id="next-btn">下一页</button>
    </div>

    <div class="presentation-container" id="presentation">
        ${slideContents}
    </div>

    <script>
        let currentSlideIndex = 0;
        const totalSlides = ${slideFiles.length};

        // 更新幻灯片指示器
        function updateSlideIndicator() {
            document.getElementById('current-slide').textContent = currentSlideIndex + 1;
            document.getElementById('slide-indicator').textContent = \`第 \${currentSlideIndex + 1} 页\`;

            // 更新按钮状态
            document.getElementById('prev-btn').disabled = currentSlideIndex === 0;
            document.getElementById('next-btn').disabled = currentSlideIndex === totalSlides - 1;
        }

        // 滚动到指定幻灯片
        function scrollToSlide(index) {
            if (index >= 0 && index < totalSlides) {
                currentSlideIndex = index;
                const slide = document.getElementById(\`slide-\${index + 1}\`);
                if (slide) {
                    slide.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
                updateSlideIndicator();
            }
        }

        // 上一页
        function previousSlide() {
            if (currentSlideIndex > 0) {
                scrollToSlide(currentSlideIndex - 1);
            }
        }

        // 下一页
        function nextSlide() {
            if (currentSlideIndex < totalSlides - 1) {
                scrollToSlide(currentSlideIndex + 1);
            }
        }

        // 回到顶部
        function scrollToTop() {
            scrollToSlide(0);
        }

        // 全屏模式
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        // 键盘导航支持
        document.addEventListener('keydown', function(e) {
            switch(e.key) {
                case 'ArrowDown':
                case 'PageDown':
                case ' ': // 空格键
                    e.preventDefault();
                    nextSlide();
                    break;
                case 'ArrowUp':
                case 'PageUp':
                    e.preventDefault();
                    previousSlide();
                    break;
                case 'Home':
                    e.preventDefault();
                    scrollToSlide(0);
                    break;
                case 'End':
                    e.preventDefault();
                    scrollToSlide(totalSlides - 1);
                    break;
                case 'F11':
                    e.preventDefault();
                    toggleFullscreen();
                    break;
            }
        });

        // 鼠标滚轮支持
        let isScrolling = false;
        document.addEventListener('wheel', function(e) {
            if (isScrolling) return;

            isScrolling = true;
            setTimeout(() => { isScrolling = false; }, 300);

            if (e.deltaY > 0) {
                // 向下滚动
                nextSlide();
            } else {
                // 向上滚动
                previousSlide();
            }
        }, { passive: true });

        // 触摸支持（移动端）
        let touchStartY = 0;
        document.addEventListener('touchstart', function(e) {
            touchStartY = e.touches[0].clientY;
        });

        document.addEventListener('touchend', function(e) {
            const touchEndY = e.changedTouches[0].clientY;
            const diff = touchStartY - touchEndY;

            if (Math.abs(diff) > 50) { // 最小滑动距离
                if (diff > 0) {
                    nextSlide();
                } else {
                    previousSlide();
                }
            }
        });

        // 监听滚动事件，更新当前幻灯片指示器
        let scrollTimeout;
        window.addEventListener('scroll', function() {
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                const slides = document.querySelectorAll('.slide-page');
                const viewportHeight = window.innerHeight;
                const scrollTop = window.scrollY;

                for (let i = 0; i < slides.length; i++) {
                    const slide = slides[i];
                    const rect = slide.getBoundingClientRect();

                    // 如果幻灯片在视口中心附近
                    if (rect.top <= viewportHeight / 2 && rect.bottom >= viewportHeight / 2) {
                        if (currentSlideIndex !== i) {
                            currentSlideIndex = i;
                            updateSlideIndicator();
                        }
                        break;
                    }
                }
            }, 100);
        });

        // 初始化
        updateSlideIndicator();
    </script>
</body>
</html>`;
};

export default ExportButton;
