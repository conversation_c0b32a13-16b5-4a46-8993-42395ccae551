"use client";

import React, { useState, useMemo } from 'react';
import dynamic from 'next/dynamic';
import { ContentType, ViewMode } from '@/components/content-viewer/types';
import { GeneratedFile } from '@/app/content-generator/types';

// 导入导出功能组件
const ExportButton = dynamic(
  () => import('@/app/content-generator/components/export-button'),
  { ssr: false }
);

// 使用动态导入加载ContentViewer组件，并禁用SSR
const ContentViewer = dynamic(
  () => import('@/components/content-viewer/content-viewer'),
  { ssr: false }
);

// 幻灯片示例1 - 封面页
const slideExample1 = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导出功能测试 - 封面页</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body, html {
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }

        .slide {
            width: 1280px;
            height: 720px;
            background: linear-gradient(135deg, #0f172a 0%, #1e1b4b 100%);
            color: white;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 4rem;
        }

        .gradient-text {
            background: linear-gradient(90deg, #38bdf8, #a78bfa);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }

        .glow {
            position: absolute;
            border-radius: 50%;
        }

        .glow-1 {
            width: 400px;
            height: 400px;
            bottom: -150px;
            right: -100px;
            background: radial-gradient(circle, rgba(56, 189, 248, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
        }

        .glow-2 {
            width: 300px;
            height: 300px;
            top: -100px;
            left: -100px;
            background: radial-gradient(circle, rgba(167, 139, 250, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="glow glow-1"></div>
        <div class="glow glow-2"></div>

        <h1 class="text-6xl font-bold mb-6 gradient-text text-center">导出功能测试</h1>
        <p class="text-2xl text-gray-300 mb-8 text-center">幻灯片导出功能演示</p>

        <div class="w-48 h-1 bg-gradient-to-r from-blue-400 to-purple-500 mb-12"></div>

        <div class="text-center">
            <p class="text-gray-400 mb-2">测试页面 - 第1页</p>
            <p class="text-sm text-gray-500">2023年11月</p>
        </div>
    </div>
</body>
</html>`;

// 幻灯片示例2 - 内容页
const slideExample2 = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导出功能测试 - 功能特性</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body, html {
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }

        .slide {
            width: 1280px;
            height: 720px;
            background: linear-gradient(135deg, #0f172a 0%, #1e1b4b 100%);
            color: white;
            position: relative;
            padding: 4rem;
        }

        .card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(5px);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            padding: 1.5rem;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        }

        .feature-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
        }

        .icon-bg-blue {
            background: linear-gradient(135deg, #38bdf8 0%, #0284c7 100%);
        }

        .icon-bg-purple {
            background: linear-gradient(135deg, #a78bfa 0%, #7c3aed 100%);
        }

        .icon-bg-pink {
            background: linear-gradient(135deg, #fb7185 0%, #e11d48 100%);
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="mb-8">
            <h1 class="text-4xl font-bold mb-2">导出功能特性</h1>
            <div class="w-24 h-1 bg-gradient-to-r from-blue-400 to-purple-500"></div>
        </div>

        <div class="grid grid-cols-3 gap-6">
            <div class="card">
                <div class="feature-icon icon-bg-blue">
                    <i class="fas fa-file-pdf text-white text-xl"></i>
                </div>
                <h3 class="text-xl font-semibold mb-3">PDF导出</h3>
                <ul class="text-gray-300 space-y-2">
                    <li class="flex items-start">
                        <i class="fas fa-check text-blue-400 mt-1 mr-2 text-xs"></i>
                        <span>高质量PDF输出</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check text-blue-400 mt-1 mr-2 text-xs"></i>
                        <span>保持原有样式</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check text-blue-400 mt-1 mr-2 text-xs"></i>
                        <span>多页自动合并</span>
                    </li>
                </ul>
            </div>

            <div class="card">
                <div class="feature-icon icon-bg-purple">
                    <i class="fas fa-presentation text-white text-xl"></i>
                </div>
                <h3 class="text-xl font-semibold mb-3">PPT导出</h3>
                <ul class="text-gray-300 space-y-2">
                    <li class="flex items-start">
                        <i class="fas fa-check text-purple-400 mt-1 mr-2 text-xs"></i>
                        <span>多种导航方式</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check text-purple-400 mt-1 mr-2 text-xs"></i>
                        <span>鼠标滚轮支持</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check text-purple-400 mt-1 mr-2 text-xs"></i>
                        <span>全屏演示模式</span>
                    </li>
                </ul>
            </div>

            <div class="card">
                <div class="feature-icon icon-bg-pink">
                    <i class="fas fa-magic text-white text-xl"></i>
                </div>
                <h3 class="text-xl font-semibold mb-3">智能检测</h3>
                <ul class="text-gray-300 space-y-2">
                    <li class="flex items-start">
                        <i class="fas fa-check text-pink-400 mt-1 mr-2 text-xs"></i>
                        <span>自动识别幻灯片</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check text-pink-400 mt-1 mr-2 text-xs"></i>
                        <span>样式完整提取</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check text-pink-400 mt-1 mr-2 text-xs"></i>
                        <span>错误处理机制</span>
                    </li>
                </ul>
            </div>
        </div>

        <div class="absolute bottom-4 right-6 text-xs text-gray-400">测试页面 - 第2页</div>
    </div>
</body>
</html>`;

// 幻灯片示例3 - 结束页
const slideExample3 = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导出功能测试 - 测试完成</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body, html {
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }

        .slide {
            width: 1280px;
            height: 720px;
            background: linear-gradient(135deg, #0f172a 0%, #1e1b4b 100%);
            color: white;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 4rem;
        }

        .gradient-text {
            background: linear-gradient(90deg, #38bdf8, #a78bfa);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }

        .contact-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(5px);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            padding: 1.5rem;
            text-align: center;
        }

        .contact-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="text-center mb-12">
            <h1 class="text-5xl font-bold mb-4 gradient-text">测试完成</h1>
            <p class="text-xl text-gray-300 max-w-2xl mx-auto">导出功能测试已完成，您可以使用导出按钮测试PDF和PPT导出功能</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 w-full max-w-2xl">
            <div class="contact-card">
                <div class="w-16 h-16 rounded-full bg-gradient-to-r from-blue-400 to-purple-500 flex items-center justify-center mb-4 mx-auto">
                    <i class="fas fa-file-pdf text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold mb-2">PDF导出测试</h3>
                <p class="text-gray-300 text-sm">点击导出按钮选择PDF格式，测试多页合并功能</p>
            </div>

            <div class="contact-card">
                <div class="w-16 h-16 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center mb-4 mx-auto">
                    <i class="fas fa-presentation text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold mb-2">PPT导出测试</h3>
                <p class="text-gray-300 text-sm">点击导出按钮选择PPT格式，测试导航功能</p>
            </div>
        </div>

        <div class="mt-16 text-center">
            <p class="text-sm text-gray-400">© 2023 导出功能测试页面</p>
        </div>
    </div>
</body>
</html>`;

export default function TestContentViewerEnhancedPage() {
  const [contentType, setContentType] = useState<ContentType>('html');
  const [viewMode, setViewMode] = useState<ViewMode>('split');
  const [currentExample, setCurrentExample] = useState<string>('basic');

  const htmlExample = `<!DOCTYPE html>
<html>
<head>
  <title>HTML内容查看器示例</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      color: #333;
      background-color: #f9f9f9;
    }
    h1 {
      color: #2563eb;
      border-bottom: 2px solid #e5e7eb;
      padding-bottom: 10px;
      text-align: center;
    }
    h2 {
      color: #4b5563;
      margin-top: 1.5em;
    }
    .container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-top: 20px;
    }
    .card {
      flex: 1;
      min-width: 200px;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      padding: 15px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      background-color: white;
      transition: transform 0.2s, box-shadow 0.2s;
    }
    .card:hover {
      transform: translateY(-5px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    .card h3 {
      margin-top: 0;
      color: #4b5563;
      border-bottom: 1px solid #f0f0f0;
      padding-bottom: 8px;
    }
    .highlight {
      background-color: #ffffcc;
      padding: 2px 5px;
      border-radius: 3px;
    }
    .demo-table {
      width: 100%;
      border-collapse: collapse;
      margin: 20px 0;
    }
    .demo-table th, .demo-table td {
      border: 1px solid #e5e7eb;
      padding: 8px 12px;
      text-align: left;
    }
    .demo-table th {
      background-color: #f3f4f6;
    }
    .demo-table tr:nth-child(even) {
      background-color: #f9fafb;
    }
    .demo-form {
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      border: 1px solid #e5e7eb;
      margin: 20px 0;
    }
    .form-group {
      margin-bottom: 15px;
    }
    .form-group label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    .form-group input, .form-group textarea {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    .btn {
      background-color: #2563eb;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 4px;
      cursor: pointer;
    }
    .btn:hover {
      background-color: #1d4ed8;
    }
    footer {
      margin-top: 30px;
      text-align: center;
      color: #6b7280;
      font-size: 0.9em;
      border-top: 1px solid #e5e7eb;
      padding-top: 20px;
    }
  </style>
</head>
<body>
  <h1>HTML内容查看器示例</h1>

  <p>这是一个展示<span class="highlight">HTML内容查看器</span>功能的示例页面。它包含了各种HTML元素和CSS样式，用于测试渲染效果。</p>

  <h2>功能特点</h2>

  <div class="container">
    <div class="card">
      <h3>代码高亮</h3>
      <p>支持HTML代码的语法高亮显示，使代码更易读。</p>
    </div>
    <div class="card">
      <h3>实时预览</h3>
      <p>实时渲染HTML内容，显示最终效果。</p>
    </div>
    <div class="card">
      <h3>分屏模式</h3>
      <p>同时查看代码和预览，并支持调整分割比例。</p>
    </div>
  </div>

  <h2>数据表格示例</h2>

  <table class="demo-table">
    <thead>
      <tr>
        <th>功能</th>
        <th>描述</th>
        <th>支持状态</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>HTML渲染</td>
        <td>支持完整的HTML标签和CSS样式</td>
        <td>✅ 已支持</td>
      </tr>
      <tr>
        <td>代码高亮</td>
        <td>支持HTML代码语法高亮</td>
        <td>✅ 已支持</td>
      </tr>
      <tr>
        <td>分屏模式</td>
        <td>同时显示代码和预览</td>
        <td>✅ 已支持</td>
      </tr>
    </tbody>
  </table>

  <h2>表单示例</h2>

  <div class="demo-form">
    <div class="form-group">
      <label for="name">姓名</label>
      <input type="text" id="name" placeholder="请输入您的姓名">
    </div>

    <div class="form-group">
      <label for="email">电子邮箱</label>
      <input type="email" id="email" placeholder="请输入您的电子邮箱">
    </div>

    <div class="form-group">
      <label for="message">留言</label>
      <textarea id="message" rows="4" placeholder="请输入您的留言"></textarea>
    </div>

    <button class="btn">提交</button>
  </div>

  <footer>
    <p>&copy; 2023 HTML内容查看器示例 | 所有内容仅用于测试</p>
  </footer>
</body>
</html>`;

  const markdownExample = `# Markdown内容查看器示例

这是一个用于测试 **Markdown内容查看器** 功能的示例文档。本文档展示了Markdown的各种语法和渲染效果。

## 📓 标题和段落

Markdown支持多级标题，从一级标题（#）到六级标题（######）。

### 这是三级标题

#### 这是四级标题

段落之间需要有空行分隔。这样才能确保每个段落独立渲染。

这是另一个段落，展示段落分隔效果。

## 🖊️ 文本格式化

Markdown支持各种文本格式化方式：

- **粗体文本** - 使用双星号包围
- *斜体文本* - 使用单星号包围
- ***粗斜体文本*** - 使用三星号包围
- ~~删除线文本~~ - 使用双波浪线包围
- \`行内代码\` - 使用反引号包围
- <u>下划线文本</u> - 使用HTML标签
- <mark>高亮文本</mark> - 使用HTML标签
- H<sub>2</sub>O - 下标使用HTML标签
- X<sup>2</sup> - 上标使用HTML标签

## 📌 列表

### 无序列表

- 项目一
- 项目二
  - 嵌套项目 2.1
  - 嵌套项目 2.2
- 项目三

### 有序列表

1. 第一步
2. 第二步
   1. 嵌套步骤 2.1
   2. 嵌套步骤 2.2
3. 第三步

### 任务列表

- [x] 已完成任务
- [ ] 未完成任务
- [x] 另一个已完成任务

## 💻 代码块

行内代码示例：\`const greeting = "Hello World";\`

多行代码块示例：

\`\`\`javascript
function greeting(name) {
  return \`Hello, \${name}!\`;
}

console.log(greeting('World')); // 输出: Hello, World!
\`\`\`

Python代码示例：

\`\`\`python
def greeting(name):
    return f"Hello, {name}!"

print(greeting("World"))  # 输出: Hello, World!
\`\`\`

HTML代码示例：

\`\`\`html
<!DOCTYPE html>
<html>
<head>
  <title>示例</title>
</head>
<body>
  <h1>Hello World</h1>
</body>
</html>
\`\`\`

## 📃 表格

| 功能 | 描述 | 支持状态 |
|:------|:------:|------:|
| Markdown渲染 | 支持各种Markdown语法 | ✅ 已支持 |
| 代码高亮 | 支持多种语言的代码高亮 | ✅ 已支持 |
| 分屏模式 | 同时显示代码和预览 | ✅ 已支持 |

> 注意：表格中的列可以左对齐、居中或右对齐。

## 🔗 链接和图片

### 链接

- [外部链接到Markdown指南](https://www.markdownguide.org/)
- [带标题的链接](https://github.com/ "GitHub首页")
- [引用式链接][1]

[1]: https://www.markdownguide.org/basic-syntax/ "Markdown基本语法"

### 图片

![Markdown图标](https://markdown-here.com/img/icon256.png)

## 💬 引用和注释

> Markdown是一种轻量级标记语言，创建于2004年，现在已经成为世界上最流行的标记语言之一。
>
> 它的设计目标是易读易写，并且可以轻松转换为HTML。

## 🔲 水平线和分隔线

下面是一条水平线：

---

## 📚 其他Markdown扩展语法

### 定义列表

Markdown
: 一种轻量级标记语言

HTML
: 超文本标记语言

### 脚注

这是一个带有脚注的文本[^1]。

[^1]: 这是脚注的内容。

### 缩写

HTML 是一种标记语言。

*[HTML]: 超文本标记语言

## 🌐 特殊符号和表情

特殊符号：&copy; &reg; &trade; &euro; &yen; &delta; &Delta; &alpha; &omega;

表情符号：😀 😁 😂 😃 😄 🚀 👍 ❤️

---

感谢使用 **Markdown内容查看器**！如果您有任何问题或建议，请随时联系我们。`;

  // 幻灯片示例1 - 封面页
  const slideExample1 = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导出功能测试 - 封面页</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body, html {
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }

        .slide {
            width: 1280px;
            height: 720px;
            background: linear-gradient(135deg, #0f172a 0%, #1e1b4b 100%);
            color: white;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 4rem;
        }

        .gradient-text {
            background: linear-gradient(90deg, #38bdf8, #a78bfa);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }

        .glow {
            position: absolute;
            border-radius: 50%;
        }

        .glow-1 {
            width: 400px;
            height: 400px;
            bottom: -150px;
            right: -100px;
            background: radial-gradient(circle, rgba(56, 189, 248, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
        }

        .glow-2 {
            width: 300px;
            height: 300px;
            top: -100px;
            left: -100px;
            background: radial-gradient(circle, rgba(167, 139, 250, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="glow glow-1"></div>
        <div class="glow glow-2"></div>

        <h1 class="text-6xl font-bold mb-6 gradient-text text-center">导出功能测试</h1>
        <p class="text-2xl text-gray-300 mb-8 text-center">幻灯片导出功能演示</p>

        <div class="w-48 h-1 bg-gradient-to-r from-blue-400 to-purple-500 mb-12"></div>

        <div class="text-center">
            <p class="text-gray-400 mb-2">测试页面 - 第1页</p>
            <p class="text-sm text-gray-500">2023年11月</p>
        </div>
    </div>
</body>
</html>`;

  // 幻灯片示例2 - 内容页
  const slideExample2 = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导出功能测试 - 功能特性</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body, html {
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }

        .slide {
            width: 1280px;
            height: 720px;
            background: linear-gradient(135deg, #0f172a 0%, #1e1b4b 100%);
            color: white;
            position: relative;
            padding: 4rem;
        }

        .card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(5px);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            padding: 1.5rem;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        }

        .feature-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
        }

        .icon-bg-blue {
            background: linear-gradient(135deg, #38bdf8 0%, #0284c7 100%);
        }

        .icon-bg-purple {
            background: linear-gradient(135deg, #a78bfa 0%, #7c3aed 100%);
        }

        .icon-bg-pink {
            background: linear-gradient(135deg, #fb7185 0%, #e11d48 100%);
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="mb-8">
            <h1 class="text-4xl font-bold mb-2">导出功能特性</h1>
            <div class="w-24 h-1 bg-gradient-to-r from-blue-400 to-purple-500"></div>
        </div>

        <div class="grid grid-cols-3 gap-6">
            <div class="card">
                <div class="feature-icon icon-bg-blue">
                    <i class="fas fa-file-pdf text-white text-xl"></i>
                </div>
                <h3 class="text-xl font-semibold mb-3">PDF导出</h3>
                <ul class="text-gray-300 space-y-2">
                    <li class="flex items-start">
                        <i class="fas fa-check text-blue-400 mt-1 mr-2 text-xs"></i>
                        <span>高质量PDF输出</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check text-blue-400 mt-1 mr-2 text-xs"></i>
                        <span>保持原有样式</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check text-blue-400 mt-1 mr-2 text-xs"></i>
                        <span>多页自动合并</span>
                    </li>
                </ul>
            </div>

            <div class="card">
                <div class="feature-icon icon-bg-purple">
                    <i class="fas fa-presentation text-white text-xl"></i>
                </div>
                <h3 class="text-xl font-semibold mb-3">PPT导出</h3>
                <ul class="text-gray-300 space-y-2">
                    <li class="flex items-start">
                        <i class="fas fa-check text-purple-400 mt-1 mr-2 text-xs"></i>
                        <span>多种导航方式</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check text-purple-400 mt-1 mr-2 text-xs"></i>
                        <span>鼠标滚轮支持</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check text-purple-400 mt-1 mr-2 text-xs"></i>
                        <span>全屏演示模式</span>
                    </li>
                </ul>
            </div>

            <div class="card">
                <div class="feature-icon icon-bg-pink">
                    <i class="fas fa-magic text-white text-xl"></i>
                </div>
                <h3 class="text-xl font-semibold mb-3">智能检测</h3>
                <ul class="text-gray-300 space-y-2">
                    <li class="flex items-start">
                        <i class="fas fa-check text-pink-400 mt-1 mr-2 text-xs"></i>
                        <span>自动识别幻灯片</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check text-pink-400 mt-1 mr-2 text-xs"></i>
                        <span>样式完整提取</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check text-pink-400 mt-1 mr-2 text-xs"></i>
                        <span>错误处理机制</span>
                    </li>
                </ul>
            </div>
        </div>

        <div class="absolute bottom-4 right-6 text-xs text-gray-400">测试页面 - 第2页</div>
    </div>
</body>
</html>`;

  // 幻灯片示例3 - 结束页
  const slideExample3 = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导出功能测试 - 测试完成</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body, html {
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }

        .slide {
            width: 1280px;
            height: 720px;
            background: linear-gradient(135deg, #0f172a 0%, #1e1b4b 100%);
            color: white;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 4rem;
        }

        .gradient-text {
            background: linear-gradient(90deg, #38bdf8, #a78bfa);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }

        .contact-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(5px);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            padding: 1.5rem;
            text-align: center;
        }

        .contact-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="text-center mb-12">
            <h1 class="text-5xl font-bold mb-4 gradient-text">测试完成</h1>
            <p class="text-xl text-gray-300 max-w-2xl mx-auto">导出功能测试已完成，您可以使用导出按钮测试PDF和PPT导出功能</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 w-full max-w-2xl">
            <div class="contact-card">
                <div class="w-16 h-16 rounded-full bg-gradient-to-r from-blue-400 to-purple-500 flex items-center justify-center mb-4 mx-auto">
                    <i class="fas fa-file-pdf text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold mb-2">PDF导出测试</h3>
                <p class="text-gray-300 text-sm">点击导出按钮选择PDF格式，测试多页合并功能</p>
            </div>

            <div class="contact-card">
                <div class="w-16 h-16 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center mb-4 mx-auto">
                    <i class="fas fa-presentation text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold mb-2">PPT导出测试</h3>
                <p class="text-gray-300 text-sm">点击导出按钮选择PPT格式，测试导航功能</p>
            </div>
        </div>

        <div class="mt-16 text-center">
            <p class="text-sm text-gray-400">© 2023 导出功能测试页面</p>
        </div>
    </div>
</body>
</html>`;

  // 获取当前示例内容
  const getCurrentContent = () => {
    if (contentType === 'markdown') {
      return markdownExample;
    }

    switch (currentExample) {
      case 'slide1':
        return slideExample1;
      case 'slide2':
        return slideExample2;
      case 'slide3':
        return slideExample3;
      default:
        return htmlExample;
    }
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">增强版内容查看器示例</h1>

      {/* 内容类型切换按钮 */}
      <div className="flex space-x-4 mb-4">
        <button
          className={`px-4 py-2 rounded-md ${contentType === 'html' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-800'}`}
          onClick={() => {
            setContentType('html');
            setCurrentExample('basic');
          }}
        >
          HTML示例
        </button>
        <button
          className={`px-4 py-2 rounded-md ${contentType === 'markdown' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-800'}`}
          onClick={() => setContentType('markdown')}
        >
          Markdown示例
        </button>
      </div>

      {/* HTML示例选择按钮 */}
      {contentType === 'html' && (
        <div className="flex space-x-2 mb-4">
          <button
            className={`px-3 py-1.5 text-sm rounded-md ${currentExample === 'basic' ? 'bg-green-600 text-white' : 'bg-gray-200 text-gray-800'}`}
            onClick={() => setCurrentExample('basic')}
          >
            基础HTML
          </button>
          <button
            className={`px-3 py-1.5 text-sm rounded-md ${currentExample === 'slide1' ? 'bg-purple-600 text-white' : 'bg-gray-200 text-gray-800'}`}
            onClick={() => setCurrentExample('slide1')}
          >
            幻灯片1 - 封面
          </button>
          <button
            className={`px-3 py-1.5 text-sm rounded-md ${currentExample === 'slide2' ? 'bg-purple-600 text-white' : 'bg-gray-200 text-gray-800'}`}
            onClick={() => setCurrentExample('slide2')}
          >
            幻灯片2 - 内容
          </button>
          <button
            className={`px-3 py-1.5 text-sm rounded-md ${currentExample === 'slide3' ? 'bg-purple-600 text-white' : 'bg-gray-200 text-gray-800'}`}
            onClick={() => setCurrentExample('slide3')}
          >
            幻灯片3 - 结束
          </button>
        </div>
      )}

      {/* 视图模式切换按钮 */}
      <div className="flex space-x-4 mb-4">
        <button
          className={`px-4 py-2 rounded-md ${viewMode === 'code' ? 'bg-green-600 text-white' : 'bg-gray-200 text-gray-800'}`}
          onClick={() => setViewMode('code')}
        >
          仅代码
        </button>
        <button
          className={`px-4 py-2 rounded-md ${viewMode === 'preview' ? 'bg-green-600 text-white' : 'bg-gray-200 text-gray-800'}`}
          onClick={() => setViewMode('preview')}
        >
          仅预览
        </button>
        <button
          className={`px-4 py-2 rounded-md ${viewMode === 'split' ? 'bg-green-600 text-white' : 'bg-gray-200 text-gray-800'}`}
          onClick={() => setViewMode('split')}
        >
          分屏模式
        </button>
      </div>

      {/* 当前内容类型和视图模式信息 */}
      <div className="mb-4 p-3 bg-gray-100 rounded-md">
        <p className="text-gray-700">
          当前内容类型: <span className="font-semibold">{contentType === 'html' ? 'HTML' : 'Markdown'}</span> |
          当前视图模式: <span className="font-semibold">
            {viewMode === 'code' ? '仅代码' : viewMode === 'preview' ? '仅预览' : '分屏模式'}
          </span>
          {contentType === 'html' && (
            <span> | 当前示例: <span className="font-semibold">
              {currentExample === 'basic' ? '基础HTML' :
               currentExample === 'slide1' ? '幻灯片1 - 封面' :
               currentExample === 'slide2' ? '幻灯片2 - 内容' :
               currentExample === 'slide3' ? '幻灯片3 - 结束' : '未知'}
            </span></span>
          )}
        </p>
        {currentExample.startsWith('slide') && (
          <div className="mt-2 p-2 bg-purple-50 border border-purple-200 rounded">
            <p className="text-purple-700 text-sm">
              <i className="fas fa-info-circle mr-1"></i>
              这是幻灯片示例，包含 <code>class="slide"</code> 属性，可以测试导出功能。
              {currentExample === 'slide1' && '这是封面页示例。'}
              {currentExample === 'slide2' && '这是内容页示例，展示了导出功能的特性。'}
              {currentExample === 'slide3' && '这是结束页示例，包含测试说明。'}
            </p>
          </div>
        )}
      </div>

      <div className="h-[600px] border rounded-lg overflow-hidden">
        <ContentViewer
          content={getCurrentContent()}
          contentType={contentType}
          initialViewMode={viewMode}
          onContentTypeChange={setContentType}
          onViewModeChange={setViewMode}
        />
      </div>

      {/* 导出功能测试说明 */}
      {currentExample.startsWith('slide') && (
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="text-lg font-semibold text-blue-800 mb-3">
            <i className="fas fa-download mr-2"></i>
            导出功能测试说明
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-white p-3 rounded border">
              <h4 className="font-semibold text-gray-800 mb-2">
                <i className="fas fa-file-pdf text-red-500 mr-1"></i>
                PDF导出测试
              </h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 在内容生成器中生成多个幻灯片文件</li>
                <li>• 点击"导出"按钮选择PDF格式</li>
                <li>• 验证多页自动合并功能</li>
                <li>• 检查样式和排版是否保持完整</li>
              </ul>
            </div>
            <div className="bg-white p-3 rounded border">
              <h4 className="font-semibold text-gray-800 mb-2">
                <i className="fas fa-presentation text-purple-500 mr-1"></i>
                PPT导出测试
              </h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 在内容生成器中生成多个幻灯片文件</li>
                <li>• 点击"导出"按钮选择PPT格式</li>
                <li>• 测试鼠标滚轮导航功能</li>
                <li>• 验证键盘导航和全屏模式</li>
              </ul>
            </div>
          </div>
          <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
            <p className="text-sm text-yellow-800">
              <i className="fas fa-lightbulb mr-1"></i>
              <strong>提示：</strong>要测试完整的导出功能，请访问
              <a href="/content-generator-stream" className="text-blue-600 hover:underline mx-1">内容生成器页面</a>
              生成包含多个幻灯片的内容，然后使用导出功能。
            </p>
          </div>
        </div>
      )}

      {/* 导出功能实际测试区域 */}
      {currentExample.startsWith('slide') && (
        <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
          <h3 className="text-lg font-semibold text-green-800 mb-3">
            <i className="fas fa-flask mr-2"></i>
            导出功能实际测试
          </h3>
          <p className="text-sm text-green-700 mb-4">
            下面的导出按钮使用真实的导出功能，您可以直接测试当前显示的幻灯片内容的导出效果。
          </p>
          <div className="space-y-4">
            <ExportTestComponent
              slideContent={getCurrentContent()}
              slideName={`test-slide-${currentExample}`}
            />
            <MultiSlideExportTest />
          </div>
        </div>
      )}
    </div>
  );
}

// 导出测试组件
interface ExportTestComponentProps {
  slideContent: string;
  slideName: string;
}

const ExportTestComponent: React.FC<ExportTestComponentProps> = ({ slideContent, slideName }) => {
  // 创建模拟的GeneratedFile数组用于测试
  const testFiles = useMemo((): GeneratedFile[] => [
    {
      id: `test-${Date.now()}`,
      name: `${slideName}.html`,
      content: slideContent,
      contentType: 'html',
      status: 'completed',
      viewMode: 'preview',
      timestamp: Date.now()
    }
  ], [slideContent, slideName]);

  return (
    <div className="flex items-center justify-between p-3 bg-white rounded border">
      <div>
        <h4 className="font-semibold text-gray-800">当前幻灯片导出测试</h4>
        <p className="text-sm text-gray-600">
          文件名: {slideName}.html |
          状态: <span className="text-green-600">已完成</span> |
          类型: <span className="text-blue-600">HTML幻灯片</span>
        </p>
      </div>
      <div className="flex items-center gap-2">
        <ExportButton
          files={testFiles}
          disabled={false}
        />
        <div className="text-xs text-gray-500">
          <i className="fas fa-info-circle mr-1"></i>
          单文件测试
        </div>
      </div>
    </div>
  );
};

// 多文件导出测试组件
const MultiSlideExportTest: React.FC = () => {
  // 创建包含所有三个幻灯片的测试文件数组
  const multiTestFiles = useMemo((): GeneratedFile[] => [
    {
      id: `test-multi-1-${Date.now()}`,
      name: 'test-slide-1-cover.html',
      content: slideExample1,
      contentType: 'html',
      status: 'completed',
      viewMode: 'preview',
      timestamp: Date.now()
    },
    {
      id: `test-multi-2-${Date.now()}`,
      name: 'test-slide-2-content.html',
      content: slideExample2,
      contentType: 'html',
      status: 'completed',
      viewMode: 'preview',
      timestamp: Date.now() + 1
    },
    {
      id: `test-multi-3-${Date.now()}`,
      name: 'test-slide-3-ending.html',
      content: slideExample3,
      contentType: 'html',
      status: 'completed',
      viewMode: 'preview',
      timestamp: Date.now() + 2
    }
  ], []);

  return (
    <div className="p-3 bg-white rounded border border-dashed border-green-300">
      <div className="flex items-center justify-between">
        <div>
          <h4 className="font-semibold text-gray-800">多页幻灯片导出测试</h4>
          <p className="text-sm text-gray-600">
            包含3个幻灯片文件 |
            状态: <span className="text-green-600">全部已完成</span> |
            类型: <span className="text-blue-600">HTML幻灯片集合</span>
          </p>
          <div className="mt-2 text-xs text-gray-500">
            <div>• test-slide-1-cover.html (封面页)</div>
            <div>• test-slide-2-content.html (内容页)</div>
            <div>• test-slide-3-ending.html (结束页)</div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <ExportButton
            files={multiTestFiles}
            disabled={false}
          />
          <div className="text-xs text-gray-500">
            <i className="fas fa-layer-group mr-1"></i>
            多文件测试
          </div>
        </div>
      </div>
    </div>
  );
};
